# 配置服务开发环境配置文件
# 此配置专门用于开发环境，禁用了用户认证以简化开发流程
# ⚠️ 警告：此配置仅适用于开发环境，绝不能在生产环境使用！

# 服务器配置
server:
  port: 8083                          # 服务端口
  mode: debug                         # 运行模式: debug
  read_timeout: 30s                   # 读取超时时间
  write_timeout: 30s                  # 写入超时时间
  idle_timeout: 60s                   # 空闲超时时间

# 数据库配置 - 开发环境
database:
  driver: sqlite                      # 数据库驱动
  dsn: "./data/config-service.db"     # 数据库文件路径
  max_open_conns: 25                  # 最大打开连接数
  max_idle_conns: 5                   # 最大空闲连接数
  conn_max_lifetime: 1h               # 连接最大生命周期
  log_level: info                     # 数据库日志级别

# Redis配置 - 开发环境 (可选)
redis:
  addr: "localhost:6379"              # Redis地址
  password: ""                        # Redis密码
  db: 2                              # Redis数据库编号
  pool_size: 5                       # 连接池大小
  min_idle_conns: 2                  # 最小空闲连接数
  dial_timeout: 5s                   # 连接超时时间

# 日志配置 - 开发环境
log:
  level: debug                        # 日志级别: debug
  format: text                        # 日志格式: text
  output: stdout                      # 日志输出
  service_name: "config-service-dev"  # 服务名称

# 🔓 安全配置 - 开发环境认证设置
security:
  # JWT配置 - 开发环境跳过认证
  jwt:
    enabled: false                    # ❌ 禁用JWT校验
    dev_mode: true                    # ✅ 启用开发模式
    dev_token: "config-dev-token-2024" # 🔑 开发令牌
    secret: "config-dev-jwt-secret"   # 开发JWT密钥
    expires_in: 24h                   # Token过期时间
    
    # 🧑‍💻 开发模式用户信息
    dev_user:
      id: "config-dev-user-001"       # 开发用户ID
      tenant_id: "dev-tenant-001"     # 开发租户ID
      username: "配置管理员"           # 开发用户名
      email: "config-dev@localhost"   # 开发用户邮箱
      roles: ["admin", "config_manager"] # 配置管理权限
      permissions: ["config:*"]       # 配置相关所有权限
    
    # 跳过认证的路径
    skip_paths:
      - "/health"
      - "/ready"
      - "/metrics"
      - "/swagger"
      - "/swagger/*"
      - "/ws"                         # WebSocket连接
  
  # 权限配置 - 开发环境
  rbac:
    enabled: false                    # 禁用RBAC (开发环境)
    default_role: "admin"             # 默认管理员角色
  
  # 审计配置 - 开发环境
  audit:
    enabled: true                     # 启用审计 (用于调试)
    log_requests: true                # 记录请求
    log_responses: false              # 不记录响应

# 服务配置 - 开发环境优化
service:
  # 缓存配置
  cache:
    enabled: true                     # 启用缓存
    ttl: "10m"                       # 缓存过期时间 - 开发环境较短
    max_size: 500                    # 最大缓存条目数
  
  # 版本管理 - 开发环境
  version:
    limit: 50                        # 保留的版本数量 - 开发环境较少
    auto_cleanup: true               # 自动清理旧版本
  
  # 审计配置
  audit:
    enabled: true                    # 启用审计
    retention: "30d"                 # 审计日志保留时间 - 开发环境较短
  
  # 验证配置 - 开发环境宽松验证
  validation:
    enabled: true                    # 启用验证
    strict: false                    # 非严格模式
    allow_unknown_fields: true       # 允许未知字段
  
  # 热更新配置
  hot_reload:
    enabled: true                    # 启用热更新
    interval: "10s"                  # 检查间隔 - 开发环境更频繁
    auto_restart: true               # 自动重启服务
  
  # 权限配置 - 开发环境
  permissions:
    enabled: false                   # 禁用权限检查 (开发环境)
    default_role: "admin"            # 默认管理员角色
  
  # 分发配置
  distribution:
    enabled: true                    # 启用分发
    timeout: "15s"                   # 分发超时时间 - 开发环境较短
    retry_count: 2                   # 重试次数 - 开发环境较少
    batch_size: 10                   # 批处理大小
  
  # WebSocket配置
  websocket:
    enabled: true                    # 启用WebSocket
    ping_interval: "30s"             # Ping间隔 - 开发环境较短
    pong_timeout: "40s"              # Pong超时
    buffer_size: 128                 # 发送缓冲区大小 - 开发环境较小

# 存储配置 - 开发环境
storage:
  # 配置存储
  config:
    type: "file"                     # 存储类型: file (文件存储)
    base_path: "./data/configs"      # 配置文件基础路径
    backup_enabled: true             # 启用备份
    backup_interval: "1h"            # 备份间隔 - 开发环境较频繁
    max_backups: 10                  # 最大备份数量 - 开发环境较少
  
  # 模板存储
  template:
    base_path: "./data/templates"    # 模板基础路径
    cache_enabled: true              # 启用模板缓存
    cache_ttl: "30m"                # 缓存过期时间

# 集成配置 - 开发环境
integrations:
  # 服务发现 - 开发环境简化
  discovery:
    enabled: false                   # 禁用服务发现 (开发环境)
    type: "static"                   # 静态配置
  
  # 外部配置源 - 开发环境
  external_sources:
    enabled: false                   # 禁用外部配置源 (开发环境)
  
  # 配置中心集成
  config_center:
    enabled: false                   # 禁用配置中心集成 (开发环境)

# 监控配置 - 开发环境
monitoring:
  # 指标收集
  metrics:
    enabled: true                    # 启用指标收集
    endpoint: "/metrics"             # 指标端点
    interval: "30s"                  # 收集间隔
    detailed: true                   # 详细指标 (开发环境)
  
  # 健康检查
  health:
    check_interval: "10s"            # 健康检查间隔 - 开发环境更频繁
    timeout: "5s"                    # 健康检查超时
    detailed: true                   # 详细健康信息
  
  # 性能监控
  performance:
    enabled: true                    # 启用性能监控
    slow_query_threshold: "100ms"    # 慢查询阈值 - 开发环境较低
    trace_enabled: true              # 启用链路追踪

# 🔧 开发环境特殊配置
development:
  # 调试功能
  debug: true                        # 启用调试模式
  pprof:
    enabled: true                    # 启用性能分析
    port: 6063                       # pprof端口
  
  # 热重载
  hot_reload:
    enabled: true                    # 启用热重载
    watch_paths: ["./internal/config", "./configs"] # 监控路径
    auto_restart: true               # 自动重启
  
  # 测试数据
  test_data:
    enabled: true                    # 启用测试数据
    load_on_startup: true            # 启动时加载
    sample_configs: true             # 加载示例配置
  
  # API文档
  swagger:
    enabled: true                    # 启用Swagger文档
    path: "/swagger"                 # 文档路径
    detailed: true                   # 详细文档
  
  # 开发工具
  dev_tools:
    enabled: true                    # 启用开发工具
    config_editor: true              # 配置编辑器
    log_viewer: true                 # 日志查看器
    config_validator: true           # 配置验证器
    diff_viewer: true                # 差异查看器

# 🚨 开发模式警告
warnings:
  show_dev_mode_warning: true
  dev_mode_banner: |
    ⚠️  配置服务开发模式已启用
    🔓 用户认证已禁用，所有配置操作将使用默认开发用户
    📝 配置文件将存储在本地文件系统
    🔄 热重载已启用，配置变更将自动生效
    🚨 此配置仅适用于开发环境！
