import Cookies from 'js-cookie'

// Token存储键名
const TOKEN_KEY = 'paas_access_token'
const REFRESH_TOKEN_KEY = 'paas_refresh_token'

/**
 * 获取访问token
 */
export function getToken(): string | undefined {
  return Cookies.get(TOKEN_KEY)
}

/**
 * 设置访问token
 * @param token 访问token
 */
export function setToken(token: string): void {
  Cookies.set(TOKEN_KEY, token, { 
    expires: 1, // 1天过期
    secure: location.protocol === 'https:',
    sameSite: 'strict'
  })
}

/**
 * 移除访问token
 */
export function removeToken(): void {
  Cookies.remove(TOKEN_KEY)
  Cookies.remove(REFRESH_TOKEN_KEY)
}

/**
 * 获取刷新token
 */
export function getRefreshToken(): string | undefined {
  return Cookies.get(REFRESH_TOKEN_KEY)
}

/**
 * 设置刷新token
 * @param refreshToken 刷新token
 */
export function setRefreshToken(refreshToken: string): void {
  Cookies.set(REFRESH_TOKEN_KEY, refreshToken, {
    expires: 7, // 7天过期
    secure: location.protocol === 'https:',
    sameSite: 'strict',
    httpOnly: false // 注意：在生产环境中应该设置为true
  })
}

/**
 * 检查token是否即将过期
 * @param token JWT token
 * @returns 是否即将过期（30分钟内）
 */
export function isTokenExpiringSoon(token: string): boolean {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const exp = payload.exp * 1000 // 转换为毫秒
    const now = Date.now()
    const thirtyMinutes = 30 * 60 * 1000
    
    return exp - now < thirtyMinutes
  } catch (error) {
    console.error('解析token失败:', error)
    return true // 解析失败认为已过期
  }
}

/**
 * 从token中提取用户信息
 * @param token JWT token
 */
export function parseTokenPayload(token: string): any {
  try {
    return JSON.parse(atob(token.split('.')[1]))
  } catch (error) {
    console.error('解析token载荷失败:', error)
    return null
  }
}
