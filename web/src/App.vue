<template>
  <div id="app">
    <!-- 全局加载进度条 -->
    <div v-if="loading" class="global-loading">
      <el-loading-service />
    </div>
    
    <!-- 路由视图 -->
    <router-view />
    
    <!-- 全局通知容器 -->
    <el-backtop :right="40" :bottom="40" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 全局加载状态
const loading = ref(false)
const router = useRouter()
const userStore = useUserStore()

// 路由守卫 - 显示加载状态
router.beforeEach(() => {
  loading.value = true
})

router.afterEach(() => {
  loading.value = false
})

// 应用初始化
onMounted(async () => {
  try {
    // 尝试从本地存储恢复用户登录状态
    await userStore.initializeAuth()
  } catch (error) {
    console.warn('用户认证初始化失败:', error)
  }
})
</script>

<style lang="scss">
// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 
               'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
}

// 全局加载样式
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-darker);
}
</style>
